/* Autogenerated by mlir-tblgen; don't manually edit. */
//===----------------------------------------------------------------------===//
// Linalg Group Registration
//===----------------------------------------------------------------------===//

void mlirRegisterLinalgPasses(void) {
  registerLinalgPasses();
}

MlirPass mlirCreateLinalgConvertElementwiseToLinalgPass(void) {
  return wrap(createConvertElementwiseToLinalgPass().release());
}
void mlirRegisterLinalgConvertElementwiseToLinalgPass(void) {
  registerConvertElementwiseToLinalgPass();
}


MlirPass mlirCreateLinalgConvertLinalgToAffineLoopsPass(void) {
  return wrap(createConvertLinalgToAffineLoopsPass().release());
}
void mlirRegisterLinalgConvertLinalgToAffineLoopsPass(void) {
  registerConvertLinalgToAffineLoopsPass();
}


MlirPass mlirCreateLinalgConvertLinalgToLoopsPass(void) {
  return wrap(createConvertLinalgToLoopsPass().release());
}
void mlirRegisterLinalgConvertLinalgToLoopsPass(void) {
  registerConvertLinalgToLoopsPass();
}


MlirPass mlirCreateLinalgConvertLinalgToParallelLoopsPass(void) {
  return wrap(createConvertLinalgToParallelLoopsPass().release());
}
void mlirRegisterLinalgConvertLinalgToParallelLoopsPass(void) {
  registerConvertLinalgToParallelLoopsPass();
}


MlirPass mlirCreateLinalgLinalgBlockPackMatmul(void) {
  return wrap(createLinalgBlockPackMatmul().release());
}
void mlirRegisterLinalgLinalgBlockPackMatmul(void) {
  registerLinalgBlockPackMatmul();
}


MlirPass mlirCreateLinalgLinalgDetensorizePass(void) {
  return wrap(createLinalgDetensorizePass().release());
}
void mlirRegisterLinalgLinalgDetensorizePass(void) {
  registerLinalgDetensorizePass();
}


MlirPass mlirCreateLinalgLinalgElementwiseOpFusionPass(void) {
  return wrap(createLinalgElementwiseOpFusionPass().release());
}
void mlirRegisterLinalgLinalgElementwiseOpFusionPass(void) {
  registerLinalgElementwiseOpFusionPass();
}


MlirPass mlirCreateLinalgLinalgFoldUnitExtentDimsPass(void) {
  return wrap(createLinalgFoldUnitExtentDimsPass().release());
}
void mlirRegisterLinalgLinalgFoldUnitExtentDimsPass(void) {
  registerLinalgFoldUnitExtentDimsPass();
}


MlirPass mlirCreateLinalgLinalgGeneralizeNamedOpsPass(void) {
  return wrap(createLinalgGeneralizeNamedOpsPass().release());
}
void mlirRegisterLinalgLinalgGeneralizeNamedOpsPass(void) {
  registerLinalgGeneralizeNamedOpsPass();
}


MlirPass mlirCreateLinalgLinalgInlineScalarOperandsPass(void) {
  return wrap(createLinalgInlineScalarOperandsPass().release());
}
void mlirRegisterLinalgLinalgInlineScalarOperandsPass(void) {
  registerLinalgInlineScalarOperandsPass();
}


MlirPass mlirCreateLinalgLinalgNamedOpConversionPass(void) {
  return wrap(createLinalgNamedOpConversionPass().release());
}
void mlirRegisterLinalgLinalgNamedOpConversionPass(void) {
  registerLinalgNamedOpConversionPass();
}


MlirPass mlirCreateLinalgLinalgSpecializeGenericOpsPass(void) {
  return wrap(createLinalgSpecializeGenericOpsPass().release());
}
void mlirRegisterLinalgLinalgSpecializeGenericOpsPass(void) {
  registerLinalgSpecializeGenericOpsPass();
}

