/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_CONVERTELEMENTWISETOLINALGPASS
#define GEN_PASS_DECL_CONVERTLINAL<PERSON><PERSON>AFFINELOOPSPASS
#define GEN_PASS_DECL_CONVERTLINALGTOLOOPSPASS
#define GEN_PASS_DECL_CONVERTLINALGTOPARALLELLOOPSPASS
#define GEN_PASS_DECL_LINALGBLOCKPACKMATMUL
#define GEN_PASS_DECL_LINALGDETENSORIZEPASS
#define GEN_PASS_DECL_LINALGELEMENTWISEOPFUSIONPASS
#define GEN_PASS_DECL_LINALGFOLDUNITEXTENTDIMSPASS
#define GEN_PASS_DECL_LINALGGENERALIZENAMEDOPSPASS
#define GEN_PASS_DECL_LINALGINLINESCALAROPERANDSPASS
#define GEN_PASS_DECL_LINALGNAMED<PERSON>CONVERSIONPASS
#define GEN_PASS_DECL_LINALGSPECIALIZEGENERICOPSPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// ConvertElementwiseToLinalgPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTELEMENTWISETOLINALGPASS
std::unique_ptr<::mlir::Pass> createConvertElementwiseToLinalgPass();
#undef GEN_PASS_DECL_CONVERTELEMENTWISETOLINALGPASS
#endif // GEN_PASS_DECL_CONVERTELEMENTWISETOLINALGPASS
#ifdef GEN_PASS_DEF_CONVERTELEMENTWISETOLINALGPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertElementwiseToLinalgPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertElementwiseToLinalgPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertElementwiseToLinalgPassBase;

  ConvertElementwiseToLinalgPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertElementwiseToLinalgPassBase(const ConvertElementwiseToLinalgPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertElementwiseToLinalgPassBase& operator=(const ConvertElementwiseToLinalgPassBase &) = delete;
  ConvertElementwiseToLinalgPassBase(ConvertElementwiseToLinalgPassBase &&) = delete;
  ConvertElementwiseToLinalgPassBase& operator=(ConvertElementwiseToLinalgPassBase &&) = delete;
  ~ConvertElementwiseToLinalgPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-elementwise-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "convert-elementwise-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Convert ElementwiseMappable ops to linalg"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertElementwiseToLinalgPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertElementwiseToLinalgPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertElementwiseToLinalgPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertElementwiseToLinalgPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertElementwiseToLinalgPass() {
  return impl::createConvertElementwiseToLinalgPass();
}
#undef GEN_PASS_DEF_CONVERTELEMENTWISETOLINALGPASS
#endif // GEN_PASS_DEF_CONVERTELEMENTWISETOLINALGPASS

//===----------------------------------------------------------------------===//
// ConvertLinalgToAffineLoopsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTLINALGTOAFFINELOOPSPASS
std::unique_ptr<::mlir::Pass> createConvertLinalgToAffineLoopsPass();
#undef GEN_PASS_DECL_CONVERTLINALGTOAFFINELOOPSPASS
#endif // GEN_PASS_DECL_CONVERTLINALGTOAFFINELOOPSPASS
#ifdef GEN_PASS_DEF_CONVERTLINALGTOAFFINELOOPSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertLinalgToAffineLoopsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertLinalgToAffineLoopsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertLinalgToAffineLoopsPassBase;

  ConvertLinalgToAffineLoopsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToAffineLoopsPassBase(const ConvertLinalgToAffineLoopsPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertLinalgToAffineLoopsPassBase& operator=(const ConvertLinalgToAffineLoopsPassBase &) = delete;
  ConvertLinalgToAffineLoopsPassBase(ConvertLinalgToAffineLoopsPassBase &&) = delete;
  ConvertLinalgToAffineLoopsPassBase& operator=(ConvertLinalgToAffineLoopsPassBase &&) = delete;
  ~ConvertLinalgToAffineLoopsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-affine-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-affine-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToAffineLoopsPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToAffineLoopsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
    registry.insert<linalg::LinalgDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertLinalgToAffineLoopsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertLinalgToAffineLoopsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertLinalgToAffineLoopsPass() {
  return impl::createConvertLinalgToAffineLoopsPass();
}
#undef GEN_PASS_DEF_CONVERTLINALGTOAFFINELOOPSPASS
#endif // GEN_PASS_DEF_CONVERTLINALGTOAFFINELOOPSPASS

//===----------------------------------------------------------------------===//
// ConvertLinalgToLoopsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTLINALGTOLOOPSPASS
std::unique_ptr<::mlir::Pass> createConvertLinalgToLoopsPass();
#undef GEN_PASS_DECL_CONVERTLINALGTOLOOPSPASS
#endif // GEN_PASS_DECL_CONVERTLINALGTOLOOPSPASS
#ifdef GEN_PASS_DEF_CONVERTLINALGTOLOOPSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertLinalgToLoopsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertLinalgToLoopsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertLinalgToLoopsPassBase;

  ConvertLinalgToLoopsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToLoopsPassBase(const ConvertLinalgToLoopsPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertLinalgToLoopsPassBase& operator=(const ConvertLinalgToLoopsPassBase &) = delete;
  ConvertLinalgToLoopsPassBase(ConvertLinalgToLoopsPassBase &&) = delete;
  ConvertLinalgToLoopsPassBase& operator=(ConvertLinalgToLoopsPassBase &&) = delete;
  ~ConvertLinalgToLoopsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToLoopsPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToLoopsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
    registry.insert<scf::SCFDialect>();
    registry.insert<affine::AffineDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertLinalgToLoopsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertLinalgToLoopsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertLinalgToLoopsPass() {
  return impl::createConvertLinalgToLoopsPass();
}
#undef GEN_PASS_DEF_CONVERTLINALGTOLOOPSPASS
#endif // GEN_PASS_DEF_CONVERTLINALGTOLOOPSPASS

//===----------------------------------------------------------------------===//
// ConvertLinalgToParallelLoopsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTLINALGTOPARALLELLOOPSPASS
std::unique_ptr<::mlir::Pass> createConvertLinalgToParallelLoopsPass();
#undef GEN_PASS_DECL_CONVERTLINALGTOPARALLELLOOPSPASS
#endif // GEN_PASS_DECL_CONVERTLINALGTOPARALLELLOOPSPASS
#ifdef GEN_PASS_DEF_CONVERTLINALGTOPARALLELLOOPSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConvertLinalgToParallelLoopsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConvertLinalgToParallelLoopsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertLinalgToParallelLoopsPassBase;

  ConvertLinalgToParallelLoopsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToParallelLoopsPassBase(const ConvertLinalgToParallelLoopsPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertLinalgToParallelLoopsPassBase& operator=(const ConvertLinalgToParallelLoopsPassBase &) = delete;
  ConvertLinalgToParallelLoopsPassBase(ConvertLinalgToParallelLoopsPassBase &&) = delete;
  ConvertLinalgToParallelLoopsPassBase& operator=(ConvertLinalgToParallelLoopsPassBase &&) = delete;
  ~ConvertLinalgToParallelLoopsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-parallel-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-parallel-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToParallelLoopsPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToParallelLoopsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
    registry.insert<linalg::LinalgDialect>();
    registry.insert<memref::MemRefDialect>();
    registry.insert<scf::SCFDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertLinalgToParallelLoopsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConvertLinalgToParallelLoopsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConvertLinalgToParallelLoopsPass() {
  return impl::createConvertLinalgToParallelLoopsPass();
}
#undef GEN_PASS_DEF_CONVERTLINALGTOPARALLELLOOPSPASS
#endif // GEN_PASS_DEF_CONVERTLINALGTOPARALLELLOOPSPASS

//===----------------------------------------------------------------------===//
// LinalgBlockPackMatmul
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGBLOCKPACKMATMUL
struct LinalgBlockPackMatmulOptions {
  ::llvm::SmallVector<int64_t> blockFactors;
  bool allowPadding = true;
  ::llvm::SmallVector<int64_t> mnkPaddedSizesNextMultipleOf;
  ::llvm::SmallVector<int64_t> mnkOrder;
  bool lhsTransposeOuterBlocks = false;
  bool lhsTransposeInnerBlocks = false;
  bool rhsTransposeOuterBlocks = true;
  bool rhsTransposeInnerBlocks = true;
};
std::unique_ptr<::mlir::Pass> createLinalgBlockPackMatmul();
std::unique_ptr<::mlir::Pass> createLinalgBlockPackMatmul(LinalgBlockPackMatmulOptions options);
#undef GEN_PASS_DECL_LINALGBLOCKPACKMATMUL
#endif // GEN_PASS_DECL_LINALGBLOCKPACKMATMUL
#ifdef GEN_PASS_DEF_LINALGBLOCKPACKMATMUL

namespace impl {
  std::unique_ptr<::mlir::Pass> createLinalgBlockPackMatmul();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createLinalgBlockPackMatmul(LinalgBlockPackMatmulOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class LinalgBlockPackMatmulBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgBlockPackMatmulBase;

  LinalgBlockPackMatmulBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgBlockPackMatmulBase(const LinalgBlockPackMatmulBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgBlockPackMatmulBase& operator=(const LinalgBlockPackMatmulBase &) = delete;
  LinalgBlockPackMatmulBase(LinalgBlockPackMatmulBase &&) = delete;
  LinalgBlockPackMatmulBase& operator=(LinalgBlockPackMatmulBase &&) = delete;
  ~LinalgBlockPackMatmulBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-block-pack-matmul");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-block-pack-matmul"; }

  ::llvm::StringRef getDescription() const override { return "Convert linalg matmul ops to block layout and back"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgBlockPackMatmul");
  }
  ::llvm::StringRef getName() const override { return "LinalgBlockPackMatmul"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgBlockPackMatmulBase<DerivedT>)

  LinalgBlockPackMatmulBase(LinalgBlockPackMatmulOptions options) : LinalgBlockPackMatmulBase() {
    blockFactors = std::move(options.blockFactors);
    allowPadding = std::move(options.allowPadding);
    mnkPaddedSizesNextMultipleOf = std::move(options.mnkPaddedSizesNextMultipleOf);
    mnkOrder = std::move(options.mnkOrder);
    lhsTransposeOuterBlocks = std::move(options.lhsTransposeOuterBlocks);
    lhsTransposeInnerBlocks = std::move(options.lhsTransposeInnerBlocks);
    rhsTransposeOuterBlocks = std::move(options.rhsTransposeOuterBlocks);
    rhsTransposeInnerBlocks = std::move(options.rhsTransposeInnerBlocks);
  }
protected:
  ::mlir::Pass::ListOption<int64_t> blockFactors{*this, "block-factors", ::llvm::cl::desc("Block factors (mb, nb, kb) for relayout")};
  ::mlir::Pass::Option<bool> allowPadding{*this, "allow-padding", ::llvm::cl::desc("Allow packing padding"), ::llvm::cl::init(true)};
  ::mlir::Pass::ListOption<int64_t> mnkPaddedSizesNextMultipleOf{*this, "mnk-padded-multiples", ::llvm::cl::desc("Next multiples of the packing sizes")};
  ::mlir::Pass::ListOption<int64_t> mnkOrder{*this, "mnk-order", ::llvm::cl::desc("Permutation of matmul (M, N, K) dimensions order")};
  ::mlir::Pass::Option<bool> lhsTransposeOuterBlocks{*this, "lhs-transpose-outer-blocks", ::llvm::cl::desc("Transpose LHS outer block layout [MB][KB] -> [KB][MB]"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> lhsTransposeInnerBlocks{*this, "lhs-transpose-inner-blocks", ::llvm::cl::desc("Transpose LHS inner block layout [mb][kb] -> [kb][mb]"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> rhsTransposeOuterBlocks{*this, "rhs-transpose-outer-blocks", ::llvm::cl::desc("Transpose RHS outer block layout [KB][NB] -> [NB][KB]"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> rhsTransposeInnerBlocks{*this, "rhs-transpose-inner-blocks", ::llvm::cl::desc("Transpose RHS inner block layout [kb][nb] -> [nb][kb]"), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createLinalgBlockPackMatmul() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createLinalgBlockPackMatmul(LinalgBlockPackMatmulOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createLinalgBlockPackMatmul() {
  return impl::createLinalgBlockPackMatmul();
}

std::unique_ptr<::mlir::Pass> createLinalgBlockPackMatmul(LinalgBlockPackMatmulOptions options) {
  return impl::createLinalgBlockPackMatmul(std::move(options));
}
#undef GEN_PASS_DEF_LINALGBLOCKPACKMATMUL
#endif // GEN_PASS_DEF_LINALGBLOCKPACKMATMUL

//===----------------------------------------------------------------------===//
// LinalgDetensorizePass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGDETENSORIZEPASS
struct LinalgDetensorizePassOptions {
  bool aggressiveMode = false;
};
std::unique_ptr<::mlir::Pass> createLinalgDetensorizePass();
std::unique_ptr<::mlir::Pass> createLinalgDetensorizePass(LinalgDetensorizePassOptions options);
#undef GEN_PASS_DECL_LINALGDETENSORIZEPASS
#endif // GEN_PASS_DECL_LINALGDETENSORIZEPASS
#ifdef GEN_PASS_DEF_LINALGDETENSORIZEPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createLinalgDetensorizePass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createLinalgDetensorizePass(LinalgDetensorizePassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class LinalgDetensorizePassBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = LinalgDetensorizePassBase;

  LinalgDetensorizePassBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgDetensorizePassBase(const LinalgDetensorizePassBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}
  LinalgDetensorizePassBase& operator=(const LinalgDetensorizePassBase &) = delete;
  LinalgDetensorizePassBase(LinalgDetensorizePassBase &&) = delete;
  LinalgDetensorizePassBase& operator=(LinalgDetensorizePassBase &&) = delete;
  ~LinalgDetensorizePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-detensorize");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-detensorize"; }

  ::llvm::StringRef getDescription() const override { return "Detensorize linalg ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgDetensorizePass");
  }
  ::llvm::StringRef getName() const override { return "LinalgDetensorizePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgDetensorizePassBase<DerivedT>)

  LinalgDetensorizePassBase(LinalgDetensorizePassOptions options) : LinalgDetensorizePassBase() {
    aggressiveMode = std::move(options.aggressiveMode);
  }
protected:
  ::mlir::Pass::Option<bool> aggressiveMode{*this, "aggressive-mode", ::llvm::cl::desc("Detensorize all ops that qualify for detensoring along with branch operands and basic-block arguments."), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createLinalgDetensorizePass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createLinalgDetensorizePass(LinalgDetensorizePassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createLinalgDetensorizePass() {
  return impl::createLinalgDetensorizePass();
}

std::unique_ptr<::mlir::Pass> createLinalgDetensorizePass(LinalgDetensorizePassOptions options) {
  return impl::createLinalgDetensorizePass(std::move(options));
}
#undef GEN_PASS_DEF_LINALGDETENSORIZEPASS
#endif // GEN_PASS_DEF_LINALGDETENSORIZEPASS

//===----------------------------------------------------------------------===//
// LinalgElementwiseOpFusionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGELEMENTWISEOPFUSIONPASS
std::unique_ptr<::mlir::Pass> createLinalgElementwiseOpFusionPass();
#undef GEN_PASS_DECL_LINALGELEMENTWISEOPFUSIONPASS
#endif // GEN_PASS_DECL_LINALGELEMENTWISEOPFUSIONPASS
#ifdef GEN_PASS_DEF_LINALGELEMENTWISEOPFUSIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createLinalgElementwiseOpFusionPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class LinalgElementwiseOpFusionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgElementwiseOpFusionPassBase;

  LinalgElementwiseOpFusionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgElementwiseOpFusionPassBase(const LinalgElementwiseOpFusionPassBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgElementwiseOpFusionPassBase& operator=(const LinalgElementwiseOpFusionPassBase &) = delete;
  LinalgElementwiseOpFusionPassBase(LinalgElementwiseOpFusionPassBase &&) = delete;
  LinalgElementwiseOpFusionPassBase& operator=(LinalgElementwiseOpFusionPassBase &&) = delete;
  ~LinalgElementwiseOpFusionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fuse-elementwise-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fuse-elementwise-ops"; }

  ::llvm::StringRef getDescription() const override { return "Fuse elementwise operations on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgElementwiseOpFusionPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgElementwiseOpFusionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
    registry.insert<linalg::LinalgDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgElementwiseOpFusionPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createLinalgElementwiseOpFusionPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createLinalgElementwiseOpFusionPass() {
  return impl::createLinalgElementwiseOpFusionPass();
}
#undef GEN_PASS_DEF_LINALGELEMENTWISEOPFUSIONPASS
#endif // GEN_PASS_DEF_LINALGELEMENTWISEOPFUSIONPASS

//===----------------------------------------------------------------------===//
// LinalgFoldUnitExtentDimsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGFOLDUNITEXTENTDIMSPASS
struct LinalgFoldUnitExtentDimsPassOptions {
  bool useRankReducingSlices = false;
};
std::unique_ptr<::mlir::Pass> createLinalgFoldUnitExtentDimsPass();
std::unique_ptr<::mlir::Pass> createLinalgFoldUnitExtentDimsPass(LinalgFoldUnitExtentDimsPassOptions options);
#undef GEN_PASS_DECL_LINALGFOLDUNITEXTENTDIMSPASS
#endif // GEN_PASS_DECL_LINALGFOLDUNITEXTENTDIMSPASS
#ifdef GEN_PASS_DEF_LINALGFOLDUNITEXTENTDIMSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createLinalgFoldUnitExtentDimsPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createLinalgFoldUnitExtentDimsPass(LinalgFoldUnitExtentDimsPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class LinalgFoldUnitExtentDimsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgFoldUnitExtentDimsPassBase;

  LinalgFoldUnitExtentDimsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgFoldUnitExtentDimsPassBase(const LinalgFoldUnitExtentDimsPassBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgFoldUnitExtentDimsPassBase& operator=(const LinalgFoldUnitExtentDimsPassBase &) = delete;
  LinalgFoldUnitExtentDimsPassBase(LinalgFoldUnitExtentDimsPassBase &&) = delete;
  LinalgFoldUnitExtentDimsPassBase& operator=(LinalgFoldUnitExtentDimsPassBase &&) = delete;
  ~LinalgFoldUnitExtentDimsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fold-unit-extent-dims");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fold-unit-extent-dims"; }

  ::llvm::StringRef getDescription() const override { return "Remove unit-extent dimension in Linalg ops on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgFoldUnitExtentDimsPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgFoldUnitExtentDimsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
    registry.insert<affine::AffineDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgFoldUnitExtentDimsPassBase<DerivedT>)

  LinalgFoldUnitExtentDimsPassBase(LinalgFoldUnitExtentDimsPassOptions options) : LinalgFoldUnitExtentDimsPassBase() {
    useRankReducingSlices = std::move(options.useRankReducingSlices);
  }
protected:
  ::mlir::Pass::Option<bool> useRankReducingSlices{*this, "use-rank-reducing-slices", ::llvm::cl::desc("Generate rank-reducing slices instead of reassociative reshapes"), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createLinalgFoldUnitExtentDimsPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createLinalgFoldUnitExtentDimsPass(LinalgFoldUnitExtentDimsPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createLinalgFoldUnitExtentDimsPass() {
  return impl::createLinalgFoldUnitExtentDimsPass();
}

std::unique_ptr<::mlir::Pass> createLinalgFoldUnitExtentDimsPass(LinalgFoldUnitExtentDimsPassOptions options) {
  return impl::createLinalgFoldUnitExtentDimsPass(std::move(options));
}
#undef GEN_PASS_DEF_LINALGFOLDUNITEXTENTDIMSPASS
#endif // GEN_PASS_DEF_LINALGFOLDUNITEXTENTDIMSPASS

//===----------------------------------------------------------------------===//
// LinalgGeneralizeNamedOpsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGGENERALIZENAMEDOPSPASS
std::unique_ptr<::mlir::Pass> createLinalgGeneralizeNamedOpsPass();
#undef GEN_PASS_DECL_LINALGGENERALIZENAMEDOPSPASS
#endif // GEN_PASS_DECL_LINALGGENERALIZENAMEDOPSPASS
#ifdef GEN_PASS_DEF_LINALGGENERALIZENAMEDOPSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createLinalgGeneralizeNamedOpsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class LinalgGeneralizeNamedOpsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgGeneralizeNamedOpsPassBase;

  LinalgGeneralizeNamedOpsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgGeneralizeNamedOpsPassBase(const LinalgGeneralizeNamedOpsPassBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgGeneralizeNamedOpsPassBase& operator=(const LinalgGeneralizeNamedOpsPassBase &) = delete;
  LinalgGeneralizeNamedOpsPassBase(LinalgGeneralizeNamedOpsPassBase &&) = delete;
  LinalgGeneralizeNamedOpsPassBase& operator=(LinalgGeneralizeNamedOpsPassBase &&) = delete;
  ~LinalgGeneralizeNamedOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-generalize-named-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-generalize-named-ops"; }

  ::llvm::StringRef getDescription() const override { return "Convert named ops into generic ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgGeneralizeNamedOpsPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgGeneralizeNamedOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgGeneralizeNamedOpsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createLinalgGeneralizeNamedOpsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createLinalgGeneralizeNamedOpsPass() {
  return impl::createLinalgGeneralizeNamedOpsPass();
}
#undef GEN_PASS_DEF_LINALGGENERALIZENAMEDOPSPASS
#endif // GEN_PASS_DEF_LINALGGENERALIZENAMEDOPSPASS

//===----------------------------------------------------------------------===//
// LinalgInlineScalarOperandsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGINLINESCALAROPERANDSPASS
std::unique_ptr<::mlir::Pass> createLinalgInlineScalarOperandsPass();
#undef GEN_PASS_DECL_LINALGINLINESCALAROPERANDSPASS
#endif // GEN_PASS_DECL_LINALGINLINESCALAROPERANDSPASS
#ifdef GEN_PASS_DEF_LINALGINLINESCALAROPERANDSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createLinalgInlineScalarOperandsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class LinalgInlineScalarOperandsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgInlineScalarOperandsPassBase;

  LinalgInlineScalarOperandsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgInlineScalarOperandsPassBase(const LinalgInlineScalarOperandsPassBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgInlineScalarOperandsPassBase& operator=(const LinalgInlineScalarOperandsPassBase &) = delete;
  LinalgInlineScalarOperandsPassBase(LinalgInlineScalarOperandsPassBase &&) = delete;
  LinalgInlineScalarOperandsPassBase& operator=(LinalgInlineScalarOperandsPassBase &&) = delete;
  ~LinalgInlineScalarOperandsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-inline-scalar-operands");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-inline-scalar-operands"; }

  ::llvm::StringRef getDescription() const override { return "Inline scalar operands into linalg generic ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgInlineScalarOperandsPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgInlineScalarOperandsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgInlineScalarOperandsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createLinalgInlineScalarOperandsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createLinalgInlineScalarOperandsPass() {
  return impl::createLinalgInlineScalarOperandsPass();
}
#undef GEN_PASS_DEF_LINALGINLINESCALAROPERANDSPASS
#endif // GEN_PASS_DEF_LINALGINLINESCALAROPERANDSPASS

//===----------------------------------------------------------------------===//
// LinalgNamedOpConversionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGNAMEDOPCONVERSIONPASS
std::unique_ptr<::mlir::Pass> createLinalgNamedOpConversionPass();
#undef GEN_PASS_DECL_LINALGNAMEDOPCONVERSIONPASS
#endif // GEN_PASS_DECL_LINALGNAMEDOPCONVERSIONPASS
#ifdef GEN_PASS_DEF_LINALGNAMEDOPCONVERSIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createLinalgNamedOpConversionPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class LinalgNamedOpConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgNamedOpConversionPassBase;

  LinalgNamedOpConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgNamedOpConversionPassBase(const LinalgNamedOpConversionPassBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgNamedOpConversionPassBase& operator=(const LinalgNamedOpConversionPassBase &) = delete;
  LinalgNamedOpConversionPassBase(LinalgNamedOpConversionPassBase &&) = delete;
  LinalgNamedOpConversionPassBase& operator=(LinalgNamedOpConversionPassBase &&) = delete;
  ~LinalgNamedOpConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-named-op-conversion");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-named-op-conversion"; }

  ::llvm::StringRef getDescription() const override { return "Convert from one named linalg op to another."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgNamedOpConversionPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgNamedOpConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgNamedOpConversionPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createLinalgNamedOpConversionPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createLinalgNamedOpConversionPass() {
  return impl::createLinalgNamedOpConversionPass();
}
#undef GEN_PASS_DEF_LINALGNAMEDOPCONVERSIONPASS
#endif // GEN_PASS_DEF_LINALGNAMEDOPCONVERSIONPASS

//===----------------------------------------------------------------------===//
// LinalgSpecializeGenericOpsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LINALGSPECIALIZEGENERICOPSPASS
std::unique_ptr<::mlir::Pass> createLinalgSpecializeGenericOpsPass();
#undef GEN_PASS_DECL_LINALGSPECIALIZEGENERICOPSPASS
#endif // GEN_PASS_DECL_LINALGSPECIALIZEGENERICOPSPASS
#ifdef GEN_PASS_DEF_LINALGSPECIALIZEGENERICOPSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createLinalgSpecializeGenericOpsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class LinalgSpecializeGenericOpsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgSpecializeGenericOpsPassBase;

  LinalgSpecializeGenericOpsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgSpecializeGenericOpsPassBase(const LinalgSpecializeGenericOpsPassBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgSpecializeGenericOpsPassBase& operator=(const LinalgSpecializeGenericOpsPassBase &) = delete;
  LinalgSpecializeGenericOpsPassBase(LinalgSpecializeGenericOpsPassBase &&) = delete;
  LinalgSpecializeGenericOpsPassBase& operator=(LinalgSpecializeGenericOpsPassBase &&) = delete;
  ~LinalgSpecializeGenericOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-specialize-generic-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-specialize-generic-ops"; }

  ::llvm::StringRef getDescription() const override { return "Convert generic ops back to named ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgSpecializeGenericOpsPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgSpecializeGenericOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgSpecializeGenericOpsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createLinalgSpecializeGenericOpsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createLinalgSpecializeGenericOpsPass() {
  return impl::createLinalgSpecializeGenericOpsPass();
}
#undef GEN_PASS_DEF_LINALGSPECIALIZEGENERICOPSPASS
#endif // GEN_PASS_DEF_LINALGSPECIALIZEGENERICOPSPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ConvertElementwiseToLinalgPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertElementwiseToLinalgPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertElementwiseToLinalgPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertElementwiseToLinalgPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertElementwiseToLinalgPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertLinalgToAffineLoopsPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertLinalgToAffineLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertLinalgToAffineLoopsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertLinalgToAffineLoopsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertLinalgToAffineLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertLinalgToLoopsPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertLinalgToLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertLinalgToLoopsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertLinalgToLoopsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertLinalgToLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertLinalgToParallelLoopsPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertLinalgToParallelLoopsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertLinalgToParallelLoopsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertLinalgToParallelLoopsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertLinalgToParallelLoopsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgBlockPackMatmul Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgBlockPackMatmul() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgBlockPackMatmul();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgBlockPackMatmulPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgBlockPackMatmul();
  });
}

//===----------------------------------------------------------------------===//
// LinalgDetensorizePass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgDetensorizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgDetensorizePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgDetensorizePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgDetensorizePass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgElementwiseOpFusionPass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgElementwiseOpFusionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgElementwiseOpFusionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgElementwiseOpFusionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgElementwiseOpFusionPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgFoldUnitExtentDimsPass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgFoldUnitExtentDimsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgFoldUnitExtentDimsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgFoldUnitExtentDimsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgFoldUnitExtentDimsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgGeneralizeNamedOpsPass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgGeneralizeNamedOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgGeneralizeNamedOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgGeneralizeNamedOpsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgGeneralizeNamedOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgInlineScalarOperandsPass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgInlineScalarOperandsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgInlineScalarOperandsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgInlineScalarOperandsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgInlineScalarOperandsPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgNamedOpConversionPass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgNamedOpConversionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgNamedOpConversionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgNamedOpConversionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgNamedOpConversionPass();
  });
}

//===----------------------------------------------------------------------===//
// LinalgSpecializeGenericOpsPass Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgSpecializeGenericOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgSpecializeGenericOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLinalgSpecializeGenericOpsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLinalgSpecializeGenericOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// Linalg Registration
//===----------------------------------------------------------------------===//

inline void registerLinalgPasses() {
  registerConvertElementwiseToLinalgPass();
  registerConvertLinalgToAffineLoopsPass();
  registerConvertLinalgToLoopsPass();
  registerConvertLinalgToParallelLoopsPass();
  registerLinalgBlockPackMatmul();
  registerLinalgDetensorizePass();
  registerLinalgElementwiseOpFusionPass();
  registerLinalgFoldUnitExtentDimsPass();
  registerLinalgGeneralizeNamedOpsPass();
  registerLinalgInlineScalarOperandsPass();
  registerLinalgNamedOpConversionPass();
  registerLinalgSpecializeGenericOpsPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class ConvertElementwiseToLinalgPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertElementwiseToLinalgPassBase;

  ConvertElementwiseToLinalgPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertElementwiseToLinalgPassBase(const ConvertElementwiseToLinalgPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertElementwiseToLinalgPassBase& operator=(const ConvertElementwiseToLinalgPassBase &) = delete;
  ConvertElementwiseToLinalgPassBase(ConvertElementwiseToLinalgPassBase &&) = delete;
  ConvertElementwiseToLinalgPassBase& operator=(ConvertElementwiseToLinalgPassBase &&) = delete;
  ~ConvertElementwiseToLinalgPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-elementwise-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "convert-elementwise-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Convert ElementwiseMappable ops to linalg"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertElementwiseToLinalgPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertElementwiseToLinalgPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertElementwiseToLinalgPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertLinalgToAffineLoopsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertLinalgToAffineLoopsPassBase;

  ConvertLinalgToAffineLoopsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToAffineLoopsPassBase(const ConvertLinalgToAffineLoopsPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertLinalgToAffineLoopsPassBase& operator=(const ConvertLinalgToAffineLoopsPassBase &) = delete;
  ConvertLinalgToAffineLoopsPassBase(ConvertLinalgToAffineLoopsPassBase &&) = delete;
  ConvertLinalgToAffineLoopsPassBase& operator=(ConvertLinalgToAffineLoopsPassBase &&) = delete;
  ~ConvertLinalgToAffineLoopsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-affine-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-affine-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into affine loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToAffineLoopsPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToAffineLoopsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
    registry.insert<linalg::LinalgDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertLinalgToAffineLoopsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertLinalgToLoopsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertLinalgToLoopsPassBase;

  ConvertLinalgToLoopsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToLoopsPassBase(const ConvertLinalgToLoopsPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertLinalgToLoopsPassBase& operator=(const ConvertLinalgToLoopsPassBase &) = delete;
  ConvertLinalgToLoopsPassBase(ConvertLinalgToLoopsPassBase &&) = delete;
  ConvertLinalgToLoopsPassBase& operator=(ConvertLinalgToLoopsPassBase &&) = delete;
  ~ConvertLinalgToLoopsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToLoopsPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToLoopsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
    registry.insert<scf::SCFDialect>();
    registry.insert<affine::AffineDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertLinalgToLoopsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertLinalgToParallelLoopsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = ConvertLinalgToParallelLoopsPassBase;

  ConvertLinalgToParallelLoopsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertLinalgToParallelLoopsPassBase(const ConvertLinalgToParallelLoopsPassBase &other) : ::mlir::OperationPass<>(other) {}
  ConvertLinalgToParallelLoopsPassBase& operator=(const ConvertLinalgToParallelLoopsPassBase &) = delete;
  ConvertLinalgToParallelLoopsPassBase(ConvertLinalgToParallelLoopsPassBase &&) = delete;
  ConvertLinalgToParallelLoopsPassBase& operator=(ConvertLinalgToParallelLoopsPassBase &&) = delete;
  ~ConvertLinalgToParallelLoopsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-linalg-to-parallel-loops");
  }
  ::llvm::StringRef getArgument() const override { return "convert-linalg-to-parallel-loops"; }

  ::llvm::StringRef getDescription() const override { return "Lower the operations from the linalg dialect into parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertLinalgToParallelLoopsPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertLinalgToParallelLoopsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
    registry.insert<linalg::LinalgDialect>();
    registry.insert<memref::MemRefDialect>();
    registry.insert<scf::SCFDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertLinalgToParallelLoopsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LinalgBlockPackMatmulBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgBlockPackMatmulBase;

  LinalgBlockPackMatmulBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgBlockPackMatmulBase(const LinalgBlockPackMatmulBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgBlockPackMatmulBase& operator=(const LinalgBlockPackMatmulBase &) = delete;
  LinalgBlockPackMatmulBase(LinalgBlockPackMatmulBase &&) = delete;
  LinalgBlockPackMatmulBase& operator=(LinalgBlockPackMatmulBase &&) = delete;
  ~LinalgBlockPackMatmulBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-block-pack-matmul");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-block-pack-matmul"; }

  ::llvm::StringRef getDescription() const override { return "Convert linalg matmul ops to block layout and back"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgBlockPackMatmul");
  }
  ::llvm::StringRef getName() const override { return "LinalgBlockPackMatmul"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgBlockPackMatmulBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<int64_t> blockFactors{*this, "block-factors", ::llvm::cl::desc("Block factors (mb, nb, kb) for relayout")};
  ::mlir::Pass::Option<bool> allowPadding{*this, "allow-padding", ::llvm::cl::desc("Allow packing padding"), ::llvm::cl::init(true)};
  ::mlir::Pass::ListOption<int64_t> mnkPaddedSizesNextMultipleOf{*this, "mnk-padded-multiples", ::llvm::cl::desc("Next multiples of the packing sizes")};
  ::mlir::Pass::ListOption<int64_t> mnkOrder{*this, "mnk-order", ::llvm::cl::desc("Permutation of matmul (M, N, K) dimensions order")};
  ::mlir::Pass::Option<bool> lhsTransposeOuterBlocks{*this, "lhs-transpose-outer-blocks", ::llvm::cl::desc("Transpose LHS outer block layout [MB][KB] -> [KB][MB]"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> lhsTransposeInnerBlocks{*this, "lhs-transpose-inner-blocks", ::llvm::cl::desc("Transpose LHS inner block layout [mb][kb] -> [kb][mb]"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> rhsTransposeOuterBlocks{*this, "rhs-transpose-outer-blocks", ::llvm::cl::desc("Transpose RHS outer block layout [KB][NB] -> [NB][KB]"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> rhsTransposeInnerBlocks{*this, "rhs-transpose-inner-blocks", ::llvm::cl::desc("Transpose RHS inner block layout [kb][nb] -> [nb][kb]"), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class LinalgDetensorizePassBase : public ::mlir::InterfacePass<FunctionOpInterface> {
public:
  using Base = LinalgDetensorizePassBase;

  LinalgDetensorizePassBase() : ::mlir::InterfacePass<FunctionOpInterface>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgDetensorizePassBase(const LinalgDetensorizePassBase &other) : ::mlir::InterfacePass<FunctionOpInterface>(other) {}
  LinalgDetensorizePassBase& operator=(const LinalgDetensorizePassBase &) = delete;
  LinalgDetensorizePassBase(LinalgDetensorizePassBase &&) = delete;
  LinalgDetensorizePassBase& operator=(LinalgDetensorizePassBase &&) = delete;
  ~LinalgDetensorizePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-detensorize");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-detensorize"; }

  ::llvm::StringRef getDescription() const override { return "Detensorize linalg ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgDetensorizePass");
  }
  ::llvm::StringRef getName() const override { return "LinalgDetensorizePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgDetensorizePassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> aggressiveMode{*this, "aggressive-mode", ::llvm::cl::desc("Detensorize all ops that qualify for detensoring along with branch operands and basic-block arguments."), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class LinalgElementwiseOpFusionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgElementwiseOpFusionPassBase;

  LinalgElementwiseOpFusionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgElementwiseOpFusionPassBase(const LinalgElementwiseOpFusionPassBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgElementwiseOpFusionPassBase& operator=(const LinalgElementwiseOpFusionPassBase &) = delete;
  LinalgElementwiseOpFusionPassBase(LinalgElementwiseOpFusionPassBase &&) = delete;
  LinalgElementwiseOpFusionPassBase& operator=(LinalgElementwiseOpFusionPassBase &&) = delete;
  ~LinalgElementwiseOpFusionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fuse-elementwise-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fuse-elementwise-ops"; }

  ::llvm::StringRef getDescription() const override { return "Fuse elementwise operations on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgElementwiseOpFusionPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgElementwiseOpFusionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
    registry.insert<linalg::LinalgDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgElementwiseOpFusionPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LinalgFoldUnitExtentDimsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgFoldUnitExtentDimsPassBase;

  LinalgFoldUnitExtentDimsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgFoldUnitExtentDimsPassBase(const LinalgFoldUnitExtentDimsPassBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgFoldUnitExtentDimsPassBase& operator=(const LinalgFoldUnitExtentDimsPassBase &) = delete;
  LinalgFoldUnitExtentDimsPassBase(LinalgFoldUnitExtentDimsPassBase &&) = delete;
  LinalgFoldUnitExtentDimsPassBase& operator=(LinalgFoldUnitExtentDimsPassBase &&) = delete;
  ~LinalgFoldUnitExtentDimsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-fold-unit-extent-dims");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-fold-unit-extent-dims"; }

  ::llvm::StringRef getDescription() const override { return "Remove unit-extent dimension in Linalg ops on tensors"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgFoldUnitExtentDimsPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgFoldUnitExtentDimsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
    registry.insert<affine::AffineDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgFoldUnitExtentDimsPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> useRankReducingSlices{*this, "use-rank-reducing-slices", ::llvm::cl::desc("Generate rank-reducing slices instead of reassociative reshapes"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class LinalgGeneralizeNamedOpsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgGeneralizeNamedOpsPassBase;

  LinalgGeneralizeNamedOpsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgGeneralizeNamedOpsPassBase(const LinalgGeneralizeNamedOpsPassBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgGeneralizeNamedOpsPassBase& operator=(const LinalgGeneralizeNamedOpsPassBase &) = delete;
  LinalgGeneralizeNamedOpsPassBase(LinalgGeneralizeNamedOpsPassBase &&) = delete;
  LinalgGeneralizeNamedOpsPassBase& operator=(LinalgGeneralizeNamedOpsPassBase &&) = delete;
  ~LinalgGeneralizeNamedOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-generalize-named-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-generalize-named-ops"; }

  ::llvm::StringRef getDescription() const override { return "Convert named ops into generic ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgGeneralizeNamedOpsPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgGeneralizeNamedOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgGeneralizeNamedOpsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LinalgInlineScalarOperandsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgInlineScalarOperandsPassBase;

  LinalgInlineScalarOperandsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgInlineScalarOperandsPassBase(const LinalgInlineScalarOperandsPassBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgInlineScalarOperandsPassBase& operator=(const LinalgInlineScalarOperandsPassBase &) = delete;
  LinalgInlineScalarOperandsPassBase(LinalgInlineScalarOperandsPassBase &&) = delete;
  LinalgInlineScalarOperandsPassBase& operator=(LinalgInlineScalarOperandsPassBase &&) = delete;
  ~LinalgInlineScalarOperandsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-inline-scalar-operands");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-inline-scalar-operands"; }

  ::llvm::StringRef getDescription() const override { return "Inline scalar operands into linalg generic ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgInlineScalarOperandsPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgInlineScalarOperandsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgInlineScalarOperandsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LinalgNamedOpConversionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgNamedOpConversionPassBase;

  LinalgNamedOpConversionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgNamedOpConversionPassBase(const LinalgNamedOpConversionPassBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgNamedOpConversionPassBase& operator=(const LinalgNamedOpConversionPassBase &) = delete;
  LinalgNamedOpConversionPassBase(LinalgNamedOpConversionPassBase &&) = delete;
  LinalgNamedOpConversionPassBase& operator=(LinalgNamedOpConversionPassBase &&) = delete;
  ~LinalgNamedOpConversionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-named-op-conversion");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-named-op-conversion"; }

  ::llvm::StringRef getDescription() const override { return "Convert from one named linalg op to another."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgNamedOpConversionPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgNamedOpConversionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgNamedOpConversionPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LinalgSpecializeGenericOpsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = LinalgSpecializeGenericOpsPassBase;

  LinalgSpecializeGenericOpsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  LinalgSpecializeGenericOpsPassBase(const LinalgSpecializeGenericOpsPassBase &other) : ::mlir::OperationPass<>(other) {}
  LinalgSpecializeGenericOpsPassBase& operator=(const LinalgSpecializeGenericOpsPassBase &) = delete;
  LinalgSpecializeGenericOpsPassBase(LinalgSpecializeGenericOpsPassBase &&) = delete;
  LinalgSpecializeGenericOpsPassBase& operator=(LinalgSpecializeGenericOpsPassBase &&) = delete;
  ~LinalgSpecializeGenericOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("linalg-specialize-generic-ops");
  }
  ::llvm::StringRef getArgument() const override { return "linalg-specialize-generic-ops"; }

  ::llvm::StringRef getDescription() const override { return "Convert generic ops back to named ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LinalgSpecializeGenericOpsPass");
  }
  ::llvm::StringRef getName() const override { return "LinalgSpecializeGenericOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<linalg::LinalgDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LinalgSpecializeGenericOpsPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
