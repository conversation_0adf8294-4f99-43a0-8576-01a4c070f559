
/* Autogenerated by mlir-tblgen; don't manually edit. */

#include "mlir-c/Pass.h"

#ifdef __cplusplus
extern "C" {
#endif

// Registration for the entire group
MLIR_CAPI_EXPORTED void mlirRegisterLinalgPasses(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgConvertElementwiseToLinalgPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgConvertElementwiseToLinalgPass(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgConvertLinalgToAffineLoopsPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgConvertLinalgToAffineLoopsPass(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgConvertLinalgToLoopsPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgConvertLinalgToLoopsPass(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgConvertLinalgToParallelLoopsPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgConvertLinalgToParallelLoopsPass(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgBlockPackMatmul(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgBlockPackMatmul(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgDetensorizePass(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgDetensorizePass(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgElementwiseOpFusionPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgElementwiseOpFusionPass(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgFoldUnitExtentDimsPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgFoldUnitExtentDimsPass(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgGeneralizeNamedOpsPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgGeneralizeNamedOpsPass(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgInlineScalarOperandsPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgInlineScalarOperandsPass(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgNamedOpConversionPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgNamedOpConversionPass(void);


/* Create Linalg Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLinalgLinalgSpecializeGenericOpsPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterLinalgLinalgSpecializeGenericOpsPass(void);



#ifdef __cplusplus
}
#endif
