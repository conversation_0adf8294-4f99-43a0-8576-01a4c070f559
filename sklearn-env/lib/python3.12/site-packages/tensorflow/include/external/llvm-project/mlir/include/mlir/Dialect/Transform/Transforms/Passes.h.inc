/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_CHECKUSESPASS
#define GEN_PASS_DECL_INFEREFFECTSPASS
#define GEN_PASS_DECL_INTERPRETERPASS
#define GEN_PASS_DECL_PRELOADLIBRARYPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// CheckUsesPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CHECKUSESPASS
std::unique_ptr<::mlir::Pass> createCheckUsesPass();
#undef GEN_PASS_DECL_CHECKUSESPASS
#endif // GEN_PASS_DECL_CHECKUSESPASS
#ifdef GEN_PASS_DEF_CHECKUSESPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createCheckUsesPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class CheckUsesPassBase : public ::mlir::OperationPass<> {
public:
  using Base = CheckUsesPassBase;

  CheckUsesPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CheckUsesPassBase(const CheckUsesPassBase &other) : ::mlir::OperationPass<>(other) {}
  CheckUsesPassBase& operator=(const CheckUsesPassBase &) = delete;
  CheckUsesPassBase(CheckUsesPassBase &&) = delete;
  CheckUsesPassBase& operator=(CheckUsesPassBase &&) = delete;
  ~CheckUsesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("transform-dialect-check-uses");
  }
  ::llvm::StringRef getArgument() const override { return "transform-dialect-check-uses"; }

  ::llvm::StringRef getDescription() const override { return "warn about potential use-after-free in the transform dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CheckUsesPass");
  }
  ::llvm::StringRef getName() const override { return "CheckUsesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CheckUsesPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createCheckUsesPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createCheckUsesPass() {
  return impl::createCheckUsesPass();
}
#undef GEN_PASS_DEF_CHECKUSESPASS
#endif // GEN_PASS_DEF_CHECKUSESPASS

//===----------------------------------------------------------------------===//
// InferEffectsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_INFEREFFECTSPASS
std::unique_ptr<::mlir::Pass> createInferEffectsPass();
#undef GEN_PASS_DECL_INFEREFFECTSPASS
#endif // GEN_PASS_DECL_INFEREFFECTSPASS
#ifdef GEN_PASS_DEF_INFEREFFECTSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createInferEffectsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class InferEffectsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = InferEffectsPassBase;

  InferEffectsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  InferEffectsPassBase(const InferEffectsPassBase &other) : ::mlir::OperationPass<>(other) {}
  InferEffectsPassBase& operator=(const InferEffectsPassBase &) = delete;
  InferEffectsPassBase(InferEffectsPassBase &&) = delete;
  InferEffectsPassBase& operator=(InferEffectsPassBase &&) = delete;
  ~InferEffectsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("transform-infer-effects");
  }
  ::llvm::StringRef getArgument() const override { return "transform-infer-effects"; }

  ::llvm::StringRef getDescription() const override { return "infer transform side effects for symbols"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InferEffectsPass");
  }
  ::llvm::StringRef getName() const override { return "InferEffectsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InferEffectsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createInferEffectsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createInferEffectsPass() {
  return impl::createInferEffectsPass();
}
#undef GEN_PASS_DEF_INFEREFFECTSPASS
#endif // GEN_PASS_DEF_INFEREFFECTSPASS

//===----------------------------------------------------------------------===//
// InterpreterPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_INTERPRETERPASS
struct InterpreterPassOptions {
  std::string debugPayloadRootTag = "";
  ::llvm::SmallVector<std::string> debugBindTrailingArgs;
  bool disableExpensiveChecks = false;
  std::string entryPoint = 
              TransformDialect::kTransformEntryPointSymbolName.str()
            ;
};
std::unique_ptr<::mlir::Pass> createInterpreterPass();
std::unique_ptr<::mlir::Pass> createInterpreterPass(InterpreterPassOptions options);
#undef GEN_PASS_DECL_INTERPRETERPASS
#endif // GEN_PASS_DECL_INTERPRETERPASS
#ifdef GEN_PASS_DEF_INTERPRETERPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createInterpreterPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createInterpreterPass(InterpreterPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class InterpreterPassBase : public ::mlir::OperationPass<> {
public:
  using Base = InterpreterPassBase;

  InterpreterPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  InterpreterPassBase(const InterpreterPassBase &other) : ::mlir::OperationPass<>(other) {}
  InterpreterPassBase& operator=(const InterpreterPassBase &) = delete;
  InterpreterPassBase(InterpreterPassBase &&) = delete;
  InterpreterPassBase& operator=(InterpreterPassBase &&) = delete;
  ~InterpreterPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("transform-interpreter");
  }
  ::llvm::StringRef getArgument() const override { return "transform-interpreter"; }

  ::llvm::StringRef getDescription() const override { return "transform dialect interpreter"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InterpreterPass");
  }
  ::llvm::StringRef getName() const override { return "InterpreterPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<::mlir::transform::TransformDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InterpreterPassBase<DerivedT>)

  InterpreterPassBase(InterpreterPassOptions options) : InterpreterPassBase() {
    debugPayloadRootTag = std::move(options.debugPayloadRootTag);
    debugBindTrailingArgs = std::move(options.debugBindTrailingArgs);
    disableExpensiveChecks = std::move(options.disableExpensiveChecks);
    entryPoint = std::move(options.entryPoint);
  }
protected:
  ::mlir::Pass::Option<std::string> debugPayloadRootTag{*this, "debug-payload-root-tag", ::llvm::cl::desc("Select the operation with 'transform.target_tag' attribute having the given value as payload IR root. If empty select the pass anchor operation as the payload IR root."), ::llvm::cl::init("")};
  ::mlir::Pass::ListOption<std::string> debugBindTrailingArgs{*this, "debug-bind-trailing-args", ::llvm::cl::desc("Binds trailing arguments of the entry point to the payload operations with specified names.")};
  ::mlir::Pass::Option<bool> disableExpensiveChecks{*this, "disable-expensive-checks", ::llvm::cl::desc("Disable expensive checks in the interpreter for a faster run."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<std::string> entryPoint{*this, "entry-point", ::llvm::cl::desc("Entry point of the pass pipeline."), ::llvm::cl::init(
              TransformDialect::kTransformEntryPointSymbolName.str()
            )};
private:

  friend std::unique_ptr<::mlir::Pass> createInterpreterPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createInterpreterPass(InterpreterPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createInterpreterPass() {
  return impl::createInterpreterPass();
}

std::unique_ptr<::mlir::Pass> createInterpreterPass(InterpreterPassOptions options) {
  return impl::createInterpreterPass(std::move(options));
}
#undef GEN_PASS_DEF_INTERPRETERPASS
#endif // GEN_PASS_DEF_INTERPRETERPASS

//===----------------------------------------------------------------------===//
// PreloadLibraryPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_PRELOADLIBRARYPASS
struct PreloadLibraryPassOptions {
  ::llvm::SmallVector<std::string> transformLibraryPaths;
};
std::unique_ptr<::mlir::Pass> createPreloadLibraryPass();
std::unique_ptr<::mlir::Pass> createPreloadLibraryPass(PreloadLibraryPassOptions options);
#undef GEN_PASS_DECL_PRELOADLIBRARYPASS
#endif // GEN_PASS_DECL_PRELOADLIBRARYPASS
#ifdef GEN_PASS_DEF_PRELOADLIBRARYPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createPreloadLibraryPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createPreloadLibraryPass(PreloadLibraryPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class PreloadLibraryPassBase : public ::mlir::OperationPass<> {
public:
  using Base = PreloadLibraryPassBase;

  PreloadLibraryPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  PreloadLibraryPassBase(const PreloadLibraryPassBase &other) : ::mlir::OperationPass<>(other) {}
  PreloadLibraryPassBase& operator=(const PreloadLibraryPassBase &) = delete;
  PreloadLibraryPassBase(PreloadLibraryPassBase &&) = delete;
  PreloadLibraryPassBase& operator=(PreloadLibraryPassBase &&) = delete;
  ~PreloadLibraryPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("transform-preload-library");
  }
  ::llvm::StringRef getArgument() const override { return "transform-preload-library"; }

  ::llvm::StringRef getDescription() const override { return "preload transform dialect library"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PreloadLibraryPass");
  }
  ::llvm::StringRef getName() const override { return "PreloadLibraryPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<::mlir::transform::TransformDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PreloadLibraryPassBase<DerivedT>)

  PreloadLibraryPassBase(PreloadLibraryPassOptions options) : PreloadLibraryPassBase() {
    transformLibraryPaths = std::move(options.transformLibraryPaths);
  }
protected:
  ::mlir::Pass::ListOption<std::string> transformLibraryPaths{*this, "transform-library-paths", ::llvm::cl::desc("Optional paths to files with modules that should be merged into the transform module to provide the definitions of external named sequences.")};
private:

  friend std::unique_ptr<::mlir::Pass> createPreloadLibraryPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createPreloadLibraryPass(PreloadLibraryPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createPreloadLibraryPass() {
  return impl::createPreloadLibraryPass();
}

std::unique_ptr<::mlir::Pass> createPreloadLibraryPass(PreloadLibraryPassOptions options) {
  return impl::createPreloadLibraryPass(std::move(options));
}
#undef GEN_PASS_DEF_PRELOADLIBRARYPASS
#endif // GEN_PASS_DEF_PRELOADLIBRARYPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// CheckUsesPass Registration
//===----------------------------------------------------------------------===//

inline void registerCheckUsesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createCheckUsesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerCheckUsesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createCheckUsesPass();
  });
}

//===----------------------------------------------------------------------===//
// InferEffectsPass Registration
//===----------------------------------------------------------------------===//

inline void registerInferEffectsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createInferEffectsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerInferEffectsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createInferEffectsPass();
  });
}

//===----------------------------------------------------------------------===//
// InterpreterPass Registration
//===----------------------------------------------------------------------===//

inline void registerInterpreterPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createInterpreterPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerInterpreterPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createInterpreterPass();
  });
}

//===----------------------------------------------------------------------===//
// PreloadLibraryPass Registration
//===----------------------------------------------------------------------===//

inline void registerPreloadLibraryPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createPreloadLibraryPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerPreloadLibraryPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createPreloadLibraryPass();
  });
}

//===----------------------------------------------------------------------===//
// Transform Registration
//===----------------------------------------------------------------------===//

inline void registerTransformPasses() {
  registerCheckUsesPass();
  registerInferEffectsPass();
  registerInterpreterPass();
  registerPreloadLibraryPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class CheckUsesPassBase : public ::mlir::OperationPass<> {
public:
  using Base = CheckUsesPassBase;

  CheckUsesPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  CheckUsesPassBase(const CheckUsesPassBase &other) : ::mlir::OperationPass<>(other) {}
  CheckUsesPassBase& operator=(const CheckUsesPassBase &) = delete;
  CheckUsesPassBase(CheckUsesPassBase &&) = delete;
  CheckUsesPassBase& operator=(CheckUsesPassBase &&) = delete;
  ~CheckUsesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("transform-dialect-check-uses");
  }
  ::llvm::StringRef getArgument() const override { return "transform-dialect-check-uses"; }

  ::llvm::StringRef getDescription() const override { return "warn about potential use-after-free in the transform dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CheckUsesPass");
  }
  ::llvm::StringRef getName() const override { return "CheckUsesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CheckUsesPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class InferEffectsPassBase : public ::mlir::OperationPass<> {
public:
  using Base = InferEffectsPassBase;

  InferEffectsPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  InferEffectsPassBase(const InferEffectsPassBase &other) : ::mlir::OperationPass<>(other) {}
  InferEffectsPassBase& operator=(const InferEffectsPassBase &) = delete;
  InferEffectsPassBase(InferEffectsPassBase &&) = delete;
  InferEffectsPassBase& operator=(InferEffectsPassBase &&) = delete;
  ~InferEffectsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("transform-infer-effects");
  }
  ::llvm::StringRef getArgument() const override { return "transform-infer-effects"; }

  ::llvm::StringRef getDescription() const override { return "infer transform side effects for symbols"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InferEffectsPass");
  }
  ::llvm::StringRef getName() const override { return "InferEffectsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InferEffectsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class InterpreterPassBase : public ::mlir::OperationPass<> {
public:
  using Base = InterpreterPassBase;

  InterpreterPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  InterpreterPassBase(const InterpreterPassBase &other) : ::mlir::OperationPass<>(other) {}
  InterpreterPassBase& operator=(const InterpreterPassBase &) = delete;
  InterpreterPassBase(InterpreterPassBase &&) = delete;
  InterpreterPassBase& operator=(InterpreterPassBase &&) = delete;
  ~InterpreterPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("transform-interpreter");
  }
  ::llvm::StringRef getArgument() const override { return "transform-interpreter"; }

  ::llvm::StringRef getDescription() const override { return "transform dialect interpreter"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InterpreterPass");
  }
  ::llvm::StringRef getName() const override { return "InterpreterPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<::mlir::transform::TransformDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InterpreterPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> debugPayloadRootTag{*this, "debug-payload-root-tag", ::llvm::cl::desc("Select the operation with 'transform.target_tag' attribute having the given value as payload IR root. If empty select the pass anchor operation as the payload IR root."), ::llvm::cl::init("")};
  ::mlir::Pass::ListOption<std::string> debugBindTrailingArgs{*this, "debug-bind-trailing-args", ::llvm::cl::desc("Binds trailing arguments of the entry point to the payload operations with specified names.")};
  ::mlir::Pass::Option<bool> disableExpensiveChecks{*this, "disable-expensive-checks", ::llvm::cl::desc("Disable expensive checks in the interpreter for a faster run."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<std::string> entryPoint{*this, "entry-point", ::llvm::cl::desc("Entry point of the pass pipeline."), ::llvm::cl::init(
              TransformDialect::kTransformEntryPointSymbolName.str()
            )};
};

template <typename DerivedT>
class PreloadLibraryPassBase : public ::mlir::OperationPass<> {
public:
  using Base = PreloadLibraryPassBase;

  PreloadLibraryPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  PreloadLibraryPassBase(const PreloadLibraryPassBase &other) : ::mlir::OperationPass<>(other) {}
  PreloadLibraryPassBase& operator=(const PreloadLibraryPassBase &) = delete;
  PreloadLibraryPassBase(PreloadLibraryPassBase &&) = delete;
  PreloadLibraryPassBase& operator=(PreloadLibraryPassBase &&) = delete;
  ~PreloadLibraryPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("transform-preload-library");
  }
  ::llvm::StringRef getArgument() const override { return "transform-preload-library"; }

  ::llvm::StringRef getDescription() const override { return "preload transform dialect library"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PreloadLibraryPass");
  }
  ::llvm::StringRef getName() const override { return "PreloadLibraryPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<::mlir::transform::TransformDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PreloadLibraryPassBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<std::string> transformLibraryPaths{*this, "transform-library-paths", ::llvm::cl::desc("Optional paths to files with modules that should be merged into the transform module to provide the definitions of external named sequences.")};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
