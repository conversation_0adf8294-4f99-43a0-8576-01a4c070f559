import optparse
from typing import List

from .distribution import Distribution
from .utils import get_metadata as get_metadata

_parsed_options = tuple[optparse.Values, list[str]]

class Base:
    def __init__(self, options: _parsed_options) -> None: ...
    def finish(self) -> None: ...

class Simple(Base):
    def __init__(self, options: _parsed_options) -> None: ...
    def __call__(self, meta: Distribution) -> None: ...

class SingleLine(Base):
    def __init__(self, options: _parsed_options) -> None: ...
    def __call__(self, meta: Distribution) -> None: ...

class CSV(Base):
    def __init__(self, options: _parsed_options) -> None: ...
    def __call__(self, meta: Distribution) -> None: ...

class INI(Base):
    def __init__(self, options: _parsed_options) -> None: ...
    def __call__(self, meta: Distribution) -> None: ...
    def finish(self) -> None: ...

class JSON(Base):
    def __init__(self, options: _parsed_options) -> None: ...
    def __call__(self, meta: Distribution) -> None: ...
    def finish(self) -> None: ...

def main(args: List[str] | None = ...) -> None: ...
