Metadata-Version: 2.2
Name: pkginfo
Version: ********
Summary: Query metadata from sdists / bdists / installed packages.
Home-page: https://code.launchpad.net/~tseaver/pkginfo/trunk
Author: Tres Seaver, Agendaless Consulting
Author-email: <EMAIL>
License: MIT
Keywords: distribution sdist installed metadata
Platform: Unix
Platform: Windows
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: System :: Software Distribution
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
Provides-Extra: testing
Requires-Dist: pytest; extra == "testing"
Requires-Dist: pytest-cov; extra == "testing"
Requires-Dist: wheel; extra == "testing"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: platform
Dynamic: provides-extra
Dynamic: requires-python
Dynamic: summary

``pkginfo`` README
==================

This package provides an API for querying the distutils metadata written in
the ``PKG-INFO`` file inside a source distribution (an ``sdist``) or a
binary distribution (e.g., created by running ``bdist_egg``).  It can
also query the ``EGG-INFO`` directory of an installed distribution, and
the ``*.egg-info`` stored in a "development checkout"
(e.g, created by running ``setup.py develop``).


Please see the `pkginfo docs <http://packages.python.org/pkginfo>`_
for detailed documentation.


``pkginfo`` Changelog
=====================

******** (2025-02-19)
---------------------

- Fix *another* packaging error (more missing testcase data!).  LP #2098833

******** (2025-02-18)
---------------------

- Fix packaging error which prevents running tests from the released
  sdist.

1.12.1 (2025-02-18)
-------------------

- Disuse the metadata of the installed 'pkginfo' package for testing:
  during creation of the various 'tox' environments, different versions of
  'setuptools' will overwrite that file with different metadata (version,
  'Dynamic' headers, etc).  LP #2098742

1.12.0 (2024-12-02)
-------------------

- Declare content type of long description explicitly as 'text/x-rst'.

- Support 'metadata_version' == '2.4' (PEP 639).

1.11.3 (2024-12-02)
-------------------

- Add dependency ordering of tox environments FBO running tests in parallel.

- Fix tests broken by bump of 'metadata_version' in 'wheels' >= '0.44.0'.
  LP #2090840.

1.11.2 (2024-10-10)
-------------------

- Swap order of zip/tarball checks to work around archives which fool
  'zipfile.is_zipfile'.  LP #2084140.

1.11.1 (2024-06-08)
-------------------

- Update typing stubs, adding additional checks to 'tox -e mypy' to
  verify that they don't drift in the future.  LP #2068777.

1.11.0 (2024-05-31)
-------------------

- Add support for Python 3.13.

- Drop support for Python 3.7.

- Emit warnings for distributions parsed with unknown metadata versions,
  or metadata versions newer than highest supported version.
  LP #2058697.

- Improve exception message when failing to find a valid 'PKG-INFO' file
  within an 'sdist' archive.  LP #2064652.

1.10.0 (2024-03-03)
-------------------

- Add support for Python 3.11 and 3.12.

- Drop support for Python 3.6.

- Declare explicit testing dependency on 'wheel'.

- Add support for Metadata 2.3.


1.9.6 (2023-01-08)
------------------

- Fix various typos in docs / docstrings.  LP #2002232.

1.9.5 (2023-01-06)
------------------

- Add stricter typing checks, matching those used in 'twine'.

- Fix typing errors / gaps reported from 'twine' CI failure.  LP #2002104.

1.9.4 (2023-01-05)
------------------

- Fix packaging of stub file for Python typing support.

1.9.3 (2023-01-03)
------------------

- Added stub files for Python typing support;  verify using 'mypy'. LP #1876591.

1.9.2 (2022-11-29)
------------------

- Drop "universal" wheel support (should be redundant with
  'python_requires >= 3.6', but just in case).  LP #1998258.

1.9.1 (2022-11-29)
------------------

- Restore a deprecated alias for the '_must_decode' helper function, moved
  from 'pkginfo._compat.must_decode' to 'pkginfo.distribution._must_decode'
  in 1.90.

- Repair unit tests broken by dropping Python 2.7 classifier.

1.9.0 (2022-11-29)
------------------

- Drop support for Python 2.7.

- Switch to use 'pytest' vs. 'nose', which doesn't support Python > 3.9.

1.8.3 (2022-06-08)
------------------

- Specify supported Python versions in 'setup.py' using 'python_requires'.
  LP #1977981.

1.8.2 (2021-12-01)
------------------

- Add fix for installed distributions with '__package__' set to an empty
  string. LP #1952946.

1.8.1 (2021-11-19)
------------------

- Add 'MANIFEST.in' to ensure example files used by tests are included
  in source distributions.  LP #1951553.

1.8.0 (2021-11-18)
------------------

- Support new standard metadata location for installed dists. LP #1865286.

- Don't overwrite header-based 'description' with empty payload. LP #1885458.

- Add support for Metadata-Version 2.2. LP #1928729.

- Add support for uncompressed tarballs for sdists.  LP #1951457.

- Add support for Python 3.10.

1.7.1 (2021-07-09)
------------------

- Use Python3 to build docs, and fix doctest examples to use Python3-
  compatible syntax.  LP #1933322.

1.7.0 (2021-01-16)
------------------

- Add support for Python 3.9.

- Drop support for Python 3.5.

1.6.1 (2020-10-26)
------------------

- Adjust test classifiers to match supported Python versions. LP #1901127.

1.6.0 (2020-10-20)
------------------

- Add support for Python 3.8.
  LP #1869854.

- Drop support for Python 3.4.

- Update tests to match setuptools' change, no longer reporting metadata
  version for installed packages w/o explicit metadata.  LP #1870197.

******* (2019-01-08)
--------------------

- Fix broken 'sdist'.  LP #1639585.

1.5.0 (2019-01-07)
------------------

- Fix 'console_scripts' entry point syntax.  LP #1810734.

- Add support for JSON output from the CLI.  LP #1700580.

- Add support for installed wheels.  E.g., 'dist-info/' dirs.  LP #1700200.

- Harden metadata extraction against unexpected encodings.  LP #1780454.

- Update tests to match pip/setuptools' use of new metadata version.
  LP #1772274.

- Add support for Python 3.6 and 3.7.

- Drop support for Python 3.3.

1.4.2 (2018-03-14)
------------------

- Use relative imports in pkginfo modules.  Supports vendoring of the
  package into setuptools.

- Add support for ``Provides-Extra`` and ``Description-Content-Type`` fields.
  Per https://packaging.python.org/specifications/.  See:  PEP 566.

- Remove support for old setuptools leaving ``PKG-INFO`` in the root of
  the project directory.

1.4.1 (2016-11-07)
------------------

- Packaging only change (invalid sdist built for 1.4.0).

1.4.0 (2016-11-04)
------------------

- Relicense under MIT license:  the PSF license is not suitable for
  third-party libraries.

1.3.2 (2016-05-24)
------------------

- Packaging-only change (automate fix for wheel built for 1.3.1).

1.3.1 (2016-05-24)
------------------

- Packaging-only change (invalid wheel built for 1.3.0).

1.3.0 (2016-05-23)
------------------

- Update homepage URL to point to Launchpad, rather than PyPI.

- Add support for building wheels.

- Add support for Python 3.5.

- Drop support for Python 2.6 and 3.2.

1.2.1 (2014-01-02)
------------------

- Add overlooked Trove classifier for Python 3.4.

1.2 (2014-01-02)
----------------

- Add support for Python 3.4, PyPy3.

- Add 100% coverage for ``pkginfo.commandline`` module.

1.2b1 (2013-12-05)
------------------

- Add support for the "wheel" distribution format, along with minimal
  metadata 2.0 support (not including new PEP 426 JSON properties).
  Code (re-)borrowed from Donald Stuft's ``twine`` package.

1.1 (2013-10-09)
----------------

- Fix tests to pass with current PyPy releases.

1.1b1 (2013-05-05)
------------------

- Support "develop" packages which keep their ``*.egg-info`` in a subdirectory.
  See https://bugs.launchpad.net/pkginfo/+bug/919147.

- Add support for "unpacked SDists" (thanks to Mike Lundy for the patch).

1.0 (2013-05-05)
----------------

- No changes from 1.0b2.

1.0b2 (2012-12-28)
------------------

- Suppress resource warning leaks reported against clients.

- Fix 'commandline' module under Py3k.

1.0b1 (2012-12-28)
------------------

- Add support for Python 3.2 and 3.3, including testing them under ``tox``.

- Add support for PyPy, including testing it under ``tox``.

- Test supported Python versions under ``tox``.

- Drop support for Python 2.5.

- Add a ``setup.py dev`` alias:  runs ``setup.py develop`` and installs
  testing extras (``nose`` and ``coverage``).

0.9.1 (2012-10-22)
------------------

- Fix test failure under Python >= 2.7, which is enforcing
  'metadata_version == 1.1' because we have classifiers.


0.9 (2012-04-25)
----------------

- Fix introspection of installed namespace packages.
  They may be installed as eggs or via dist-installed 'egg-info' files.
  https://bugs.launchpad.net/pkginfo/+bug/934311

- Avoid a regression in 0.8 under Python 2.6 / 2.7 when parsing unicode.
  https://bugs.launchpad.net/pkginfo/+bug/733827/comments/3


0.8 (2011-03-12)
----------------

- Work around Python 2.7's breakage of StringIO.  Fixes
  https://bugs.launchpad.net/pkginfo/+bug/733827

- Fix bug in introspection of installed packages missing the
  ``__package__`` attribute.
  

0.7 (2010-11-04)
----------------

- Preserve newlines in the ``description`` field.  Thanks to Sridhar
  Ratnakumar for the patch.

- 100% test coverage.


0.6 (2010-06-01)
----------------

- Replace use of ``StringIO.StringIO`` with ``io.StringIO``, where available
  (Python >= 2.6).

- Replace use of ``rfc822`` stdlib module with ``email.parser``, when
  available (Python >= 2.5).  Ensured that distributions "unfold" wrapped
  continuation lines, stripping any leading / trailing whitespace, no matter
  which module was used for parsing.

- Remove bogus testing dependency on ``zope.testing``.

- Add tests that the "environment markers" spelled out in the approved
  PEP 345 are captured.

- Add ``Project-URL`` for ``1.2`` PKG-INFO metadata (defined in the accepted
  version of PEP 345).


0.5 (2009-09-11)
----------------

- Marked package as non-zip-safe.

- Fix Trove metadata misspelling.

- Restore compatibility with Python 2.4.

- Note that the introspection of installed packages / modules works only
  in Python 2.6 or later.

- Add ``Index`` class as an abstraction over a collection of distributions.

- Add ``download_url_prefix`` argument to ``pkginfo`` script.  If passed,
  the script will use the prefix to synthesize a ``download_url`` for
  distributions which do not supply that value directly.


0.4.1 (2009-05-07)
------------------

- Fix bugs in handling of installed packages which lack ``__file__``
  or ``PKG-INFO``.


0.4 (2009-05-07)
----------------

- Extend the console script to allow output as CSV or INI.  Also, added
  arguments to specify the metadata version and other parsing / output
  policies.

- Add support for the different metadata versions specified in PEPs
  241, 314, and 345.  Distributions now parse and expose only the attributes
  corresponding to their metadata version, which defaults to the version
  parsed from the ``PKG-INFO`` file.  The programmer can override that version
  when creating the distribution object.


0.3 (2009-05-07)
----------------

- Add support for introspection of "development eggs" (checkouts with
  ``PKG-INFO``, perhaps created via ``setup.py develop``).

- Add a console script, ``pkginfo``, which takes one or more paths
  on the command line and writes out the associated information.  Thanks
  to ``runeh`` for the patch!

- Add ``get_metadata`` helper function, which dispatches a given path or
  module across the available distribution types, and returns a distribution
  object.  Thanks to ``runeh`` for the patch!

- Make distribution objects support iteration over the metadata fields.
  Thanks to ``runeh`` for the patch!

- Make ``Distribution`` and subclasses new-style classes.  Thanks to ``runeh``
  for the patch!


0.2 (2009-04-14)
----------------

- Add support for introspection of ``bdist_egg`` binary distributions.


0.1.1 (2009-04-10)
------------------

- Fix packaging errors.


0.1 (2009-04-10)
----------------

- Initial release.
