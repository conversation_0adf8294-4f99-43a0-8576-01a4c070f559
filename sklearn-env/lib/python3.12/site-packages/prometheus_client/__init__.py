#!/usr/bin/env python

from . import (
    exposition, gc_collector, metrics, metrics_core, platform_collector,
    process_collector, registry,
)
from .exposition import (
    CONTENT_TYPE_LATEST, delete_from_gateway, generate_latest,
    instance_ip_grouping_key, make_asgi_app, make_wsgi_app, MetricsHandler,
    push_to_gateway, pushadd_to_gateway, start_http_server, start_wsgi_server,
    write_to_textfile,
)
from .gc_collector import GC_COLLECTOR, GCCollector
from .metrics import (
    Counter, disable_created_metrics, enable_created_metrics, Enum, Gauge,
    Histogram, Info, Summary,
)
from .metrics_core import Metric
from .platform_collector import PLATFORM_COLLECTOR, PlatformCollector
from .process_collector import PROCESS_COLLECTOR, ProcessCollector
from .registry import CollectorRegistry, REGISTRY

__all__ = (
    'CollectorRegistry',
    'REGISTRY',
    'Metric',
    'Counter',
    'Gauge',
    'Summary',
    'Histogram',
    'Info',
    'Enum',
    'enable_created_metrics',
    'disable_created_metrics',
    'CONTENT_TYPE_LATEST',
    'generate_latest',
    'MetricsHandler',
    'make_wsgi_app',
    'make_asgi_app',
    'start_http_server',
    'start_wsgi_server',
    'write_to_textfile',
    'push_to_gateway',
    'pushadd_to_gateway',
    'delete_from_gateway',
    'instance_ip_grouping_key',
    'ProcessCollector',
    'PROCESS_COLLECTOR',
    'PlatformCollector',
    'PLATFORM_COLLECTOR',
    'GCCollector',
    'GC_COLLECTOR',
)

if __name__ == '__main__':
    c = Counter('cc', 'A counter')
    c.inc()

    g = Gauge('gg', 'A gauge')
    g.set(17)

    s = Summary('ss', 'A summary', ['a', 'b'])
    s.labels('c', 'd').observe(17)

    h = Histogram('hh', 'A histogram')
    h.observe(.6)

    start_http_server(8000)
    import time

    while True:
        time.sleep(1)
