"""
<PERSON><PERSON><PERSON> to run all MLflow examples

This script runs all the example scripts and provides a comprehensive demonstration
of MLflow capabilities.
"""

import os
import sys
import subprocess
import time
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print('='*60)
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✓ Success!")
        if result.stdout:
            print("Output:")
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error: {e}")
        if e.stdout:
            print("Output:")
            print(e.stdout)
        if e.stderr:
            print("Error output:")
            print(e.stderr)
        return False


def check_dependencies():
    """Check if all required dependencies are installed"""
    print("Checking dependencies...")

    # Map package names to their import names
    package_imports = {
        'mlflow': 'mlflow',
        'pandas': 'pandas',
        'numpy': 'numpy',
        'scikit-learn': 'sklearn',  # This is the key fix!
        'matplotlib': 'matplotlib',
        'seaborn': 'seaborn'
    }
    missing_packages = []

    for package_name, import_name in package_imports.items():
        try:
            __import__(import_name)
            print(f"✓ {package_name}")
        except ImportError:
            print(f"✗ {package_name} - MISSING")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Please install them with: pip install -r requirements.txt")
        return False
    
    print("All dependencies are installed!")
    return True


def setup_environment():
    """Setup the environment for running examples"""
    # Set MLflow tracking URI to local directory
    os.environ['MLFLOW_TRACKING_URI'] = 'file:./mlruns'
    
    # Create necessary directories
    directories = ['mlruns', 'mlartifacts', 'models']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("Environment setup complete!")


def run_examples():
    """Run all example scripts"""
    
    examples = [
        {
            'script': 'examples/linear_regression_basic.py',
            'description': 'Basic Linear Regression with MLflow Tracking'
        },
        {
            'script': 'examples/classification_advanced.py',
            'description': 'Advanced Classification with Hyperparameter Tuning'
        },
        {
            'script': 'examples/model_comparison.py',
            'description': 'Comprehensive Model Comparison'
        }
    ]
    
    successful_runs = 0
    
    for example in examples:
        if os.path.exists(example['script']):
            success = run_command(f"python {example['script']}", example['description'])
            if success:
                successful_runs += 1
            time.sleep(2)  # Brief pause between examples
        else:
            print(f"✗ Script not found: {example['script']}")
    
    print(f"\n{'='*60}")
    print(f"SUMMARY: {successful_runs}/{len(examples)} examples ran successfully")
    print('='*60)
    
    return successful_runs == len(examples)


def main():
    """Main function"""
    print("MLflow Examples Runner")
    print("=" * 30)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Run examples
    success = run_examples()
    
    if success:
        print("\n🎉 All examples completed successfully!")
        print("\nNext steps:")
        print("1. Start MLflow UI: python scripts/start_mlflow.py")
        print("2. Open http://127.0.0.1:5000 in your browser")
        print("3. Explore the logged experiments, metrics, and models")
    else:
        print("\n⚠️  Some examples failed. Check the error messages above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
